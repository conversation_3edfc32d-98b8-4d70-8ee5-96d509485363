import { createFormSchema, ICreateForm } from "@/modules/inspection/validators/form/create";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";

export const useCreateForm = () => {
	const methods = useForm<ICreateForm>({
		resolver: zodResolver(createFormSchema),
		defaultValues: {
			title: "",
			text: "",
			nomenclature: "",
			developer: { id: "", name: "" },
			approver: { id: "", name: "" },
		},
		mode: "onChange",
	});

	const resetForm = () => methods.reset(methods.formState.defaultValues);

	return { methods, resetForm };
};
