import { reorderFieldGroupsAtom } from "@/modules/inspection/atoms/forms/fields/group-actions.atom";
import { fieldsGroupsAtom } from "@/modules/inspection/atoms/forms/fields/group.atom";
import { DragEndEvent, KeyboardSensor, MouseSensor, PointerSensor, TouchSensor, useSensor, useSensors } from "@dnd-kit/core";
import { useAtomValue, useSetAtom } from "jotai";
import { useId, useMemo } from "react";

export const useTableGroupsDrag = () => {
	const fieldsGroup = useAtomValue(fieldsGroupsAtom);
	const reorderGroups = useSetAtom(reorderFieldGroupsAtom);
	const sortableId = useId();
	const sensors = useSensors(
		useSensor(MouseSensor, {
			activationConstraint: {
				delay: 100,
				tolerance: 5,
			},
		}),
		useSensor(TouchSensor, {
			activationConstraint: {
				delay: 100,
				tolerance: 5,
			},
		}),
		useSensor(KeyboardSensor, {}),
		useSensor(PointerSensor, {
			activationConstraint: {
				distance: 8,
			},
		}),
	);
	const dataGroupsId = useMemo(() => fieldsGroup.map(item => item.tempId), [fieldsGroup]);

	const handleDragEnd = (event: DragEndEvent) => {
		const { active, over } = event;
		if (active && over && active.id !== over.id) {
			const fromIndex = dataGroupsId.indexOf(String(active.id));
			const toIndex = dataGroupsId.indexOf(String(over.id));

			if (fromIndex !== -1 && toIndex !== -1) {
				reorderGroups({ fromIndex, toIndex });
			}
		}
	};

	return {
		handleDragEnd,
		sensors,
		sortableId,
		dataGroupsId,
	};
};
