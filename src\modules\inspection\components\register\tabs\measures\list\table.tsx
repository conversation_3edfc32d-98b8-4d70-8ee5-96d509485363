"use client";

import useFindAllMeasures from "@/modules/inspection/hooks/measures/list/find-all.hook";
import { Pagination } from "@/shared/components/custom/pagination";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/shared/components/shadcn/table";
import { flexRender, getCoreRowModel, useReactTable } from "@tanstack/react-table";
import * as React from "react";
import { usePagination } from "../../../../../../../shared/hooks/utils";
import { columnsMeasures } from "./columns";
import { Skeleton } from "@/shared/components/shadcn/skeleton";
import { ComponentIcon } from "lucide-react";
import { EmptyStateTable } from "@/shared/components/custom/empty/table-empty";

interface IMeasuresTabProps {
	searchTerm?: string;
}

export const MedidasTab: React.FC<IMeasuresTabProps> = ({ searchTerm }) => {
	const [rowSelection, setRowSelection] = React.useState({});
	const { currentPage, setCurrentPage, pageSize, setItemsPerPage } = usePagination();

	// [ ] Aplicar loading table nesse

	const { data, pagination, isLoading, hasError, error } = useFindAllMeasures({
		page: currentPage,
		limit: pageSize,
		search: searchTerm || "",
	});

	const table = useReactTable({
		data,
		columns: columnsMeasures,
		state: { rowSelection },
		onRowSelectionChange: setRowSelection,
		enableRowSelection: true,
		getCoreRowModel: getCoreRowModel(),
		manualPagination: true,
		pageCount: pagination?.totalPages ?? 0,
	});

	const selectedCount = table.getFilteredSelectedRowModel().rows.length;

	return (
		<div className="space-y-4">
			<div className="bg-background overflow-x-auto rounded-lg border">
				<Table>
					<TableHeader className="bg-muted sticky top-0 z-10">
						{table.getHeaderGroups().map(headerGroup => (
							<TableRow key={headerGroup.id}>
								{headerGroup.headers.map(header => (
									<TableHead key={header.id} colSpan={header.colSpan} className="font-semibold whitespace-nowrap">
										{header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
									</TableHead>
								))}
							</TableRow>
						))}
					</TableHeader>
					<TableBody>
						{hasError ? (
							<TableRow>
								<TableCell colSpan={columnsMeasures.length} className="h-24 text-center text-red-500">
									Erro ao carregar os dados: {error || "Ocorreu um erro desconhecido."}
								</TableCell>
							</TableRow>
						) : isLoading ? (
							<TableRow>
								<TableCell colSpan={columnsMeasures.length} className="h-24 text-center">
									<Skeleton className="h-6 w-48" />
								</TableCell>
							</TableRow>
						) : table.getRowModel().rows.length ? (
							table.getRowModel().rows.map((row, idx) => (
								<TableRow
									key={row.id}
									data-state={row.getIsSelected() && "selected"}
									className={`${idx % 2 === 0 ? "bg-muted/20" : "bg-background"} hover:bg-primary/10 transition-colors`}
								>
									{row.getVisibleCells().map(cell => (
										<TableCell
											key={cell.id}
											className="h-[28px] max-w-[260px] overflow-hidden text-ellipsis whitespace-nowrap"
											title={String(cell.getValue() ?? "")}
										>
											{flexRender(cell.column.columnDef.cell, cell.getContext())}
										</TableCell>
									))}
								</TableRow>
							))
						) : (
							<TableRow>
								<TableCell colSpan={columnsMeasures.length} className="h-24 text-center">
									<EmptyStateTable
										searchTerm={searchTerm}
										icon={<ComponentIcon />}
										title="Nenhum componente encontrado"
										description={
											searchTerm ? "Nenhum componente corresponde ao termo pesquisado." : "Ainda não há componentes cadastrados."
										}
										tip="Você pode tentar pesquisar por outros termos ou cadastrar um novo componente."
									/>
								</TableCell>
							</TableRow>
						)}
					</TableBody>
				</Table>
			</div>
			{pagination && (
				<Pagination
					currentPage={pagination.currentPage}
					totalPages={pagination.totalPages}
					pageSize={pagination.itemsPerPage}
					totalItems={pagination.totalItems}
					selectedCount={selectedCount}
					onPageChange={setCurrentPage}
					onPageSizeChange={size => setItemsPerPage(size)}
					showPageSizeSelector
					showSelectedInfo
				/>
			)}
		</div>
	);
};
