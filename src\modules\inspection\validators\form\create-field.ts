import z from "zod";
import { InspectionFormTypeEnum } from "../../constants/form/type-enum";

export const createFieldSchema = z
	.object({
		field: z.object({
			id: z.number().min(1, "ID do campo é obrigatório"),
			name: z.string().min(1, "Nome do campo é obrigatório"),
		}),
		nickname: z.string().optional(),
		required: z.boolean().default(false),
		group: z.number().optional(),
		sequence: z.number().min(1, "Sequência é obrigatória"),
		typeId: z.nativeEnum(InspectionFormTypeEnum),
		measure: z.object({
			id: z.number().optional(),
			name: z.string().optional(),
		}),
		groupTitle: z.string().optional(),
		biFilter: z.boolean().default(false),
		id: z.string().optional(),
		options: z
			.array(
				z.object({
					sequence: z.number().min(1, "Sequência da opção é obrigatória"),
					option: z.string().min(1, "Opção é obrigatória"),
					tempId: z.string(),
					id: z.number().optional(),
				}),
			)
			.optional(),
		tempId: z.string(),
	})
	.refine(
		data => {
			if (data.typeId === InspectionFormTypeEnum.OPTIONS) {
				return data.options && data.options.length > 0;
			}
			return true;
		},
		{
			message: "Opções são obrigatórias para campos do tipo 'OPTIONS'",
			path: ["options"],
		},
	);

export type ICreateFieldForm = z.infer<typeof createFieldSchema>;
