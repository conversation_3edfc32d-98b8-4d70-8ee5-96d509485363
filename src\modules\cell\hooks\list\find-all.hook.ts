import { useQuery } from "@tanstack/react-query";
import { createGetRequest } from "../../../../shared/lib/requests";
import { IResponsePaginated } from "../../../../shared/types/requests/response-paginated.type";
import { CELL_ENDPOINTS } from "../../api/endpoints";
import { cellQueryKeys } from "../../constants/query";
import { ICellDto } from "../../types/find-all.dto";

interface IFindAllCellParams {
	page?: number;
	limit?: number;
	search?: string;
}

export const useFindAllCell = ({ page, limit, search }: IFindAllCellParams) => {
	const { data, isLoading, isFetched } = useQuery({
		queryKey: cellQueryKeys.list({ page, limit, search }),
		queryFn: () => createGetRequest<IResponsePaginated<ICellDto[]>>(CELL_ENDPOINTS.FIND_ALL({ page, limit, search })),
	});

	const isNoDataFound = !data?.success && data?.status === 404;
	return {
		data: data?.success ? data.data.data : [],
		pagination: data?.success
			? {
					totalItems: data.data.totalItems,
					itemsPerPage: data.data.itemsPerPage,
					currentPage: data.data.currentPage,
					totalPages: data.data.totalPages,
				}
			: null,
		isLoading,
		hasError: isFetched && !data?.success && !isNoDataFound,
		error: !data?.success && !isNoDataFound && data?.data.message,
		isEmpty: isNoDataFound,
	};
};
