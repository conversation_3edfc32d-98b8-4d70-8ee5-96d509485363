import { Modal } from "@/shared/components/custom/modal";
import FormCreateMeasures from "./form";
import useCreateMeasures from "@/modules/inspection/hooks/measures/create/form.hook";
import { useCreateMeasuresMutation } from "@/modules/inspection/hooks/measures/create/mutation.hook";
import { ICreateMeasures } from "@/modules/inspection/validators/measures/create";

interface IModalCreateFormMeasures {
	isOpen: boolean;
	onClose: () => void;
}

export default function ModalCreateMeasures({ isOpen, onClose }: IModalCreateFormMeasures) {
	const { methods } = useCreateMeasures();
	const { createMeasures } = useCreateMeasuresMutation();

	function handleSubmit(data: ICreateMeasures) {
		const payload = {
			...data,
			name: data.name,
			abbreviation: data.abbreviation,
		};
		createMeasures(payload);
		onClose();
		methods.reset();
	}

	return (
		<Modal isOpen={isOpen} onClose={onClose} className="!w-[500px] !max-w-none" title="Cadastro de Medidas">
			<FormCreateMeasures methods={methods} onSubmit={handleSubmit} onClose={onClose} />
		</Modal>
	);
}
