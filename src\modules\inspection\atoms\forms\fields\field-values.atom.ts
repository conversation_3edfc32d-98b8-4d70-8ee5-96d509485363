import { InspectionFormTypeEnum } from "@/modules/inspection/constants/form/type-enum";
import { atom } from "jotai";
import { fieldsGroupsAtom } from "./group.atom";

export const updateFieldNicknameAtom = atom(null, (get, set, { tempId, nickname }: { tempId: string; nickname: string }) => {
	const groupFields = get(fieldsGroupsAtom);
	const updatedGroups = groupFields.map(group => ({
		...group,
		items: group.items.map(field => (field.tempId === tempId ? { ...field, nickname } : field)),
	}));
	set(fieldsGroupsAtom, updatedGroups);
});

export const updateFieldRequiredAtom = atom(null, (get, set, { tempId, required }: { tempId: string; required: boolean }) => {
	const groupFields = get(fieldsGroupsAtom);
	const updatedGroups = groupFields.map(group => ({
		...group,
		items: group.items.map(field => (field.tempId === tempId ? { ...field, required } : field)),
	}));
	set(fieldsGroupsAtom, updatedGroups);
});

export const updateFieldGroupAtom = atom(null, (get, set, { tempId, group }: { tempId: string; group: number | undefined }) => {
	const groupFields = get(fieldsGroupsAtom);
	const updatedGroups = groupFields.map(groupItem => ({
		...groupItem,
		items: groupItem.items.map(field => (field.tempId === tempId ? { ...field, group } : field)),
	}));
	set(fieldsGroupsAtom, updatedGroups);
});

export const updateFieldGroupTitleAtom = atom(null, (get, set, { tempId, groupTitle }: { tempId: string; groupTitle: string }) => {
	const groupFields = get(fieldsGroupsAtom);
	const updatedGroups = groupFields.map(group => ({
		...group,
		items: group.items.map(field => (field.tempId === tempId ? { ...field, groupTitle } : field)),
	}));
	set(fieldsGroupsAtom, updatedGroups);
});

export const updateFieldTypeAtom = atom(null, (get, set, { tempId, typeId }: { tempId: string; typeId: InspectionFormTypeEnum }) => {
	const groupFields = get(fieldsGroupsAtom);
	const updatedGroups = groupFields.map(group => ({
		...group,
		items: group.items.map(field => (field.tempId === tempId ? { ...field, typeId } : field)),
	}));
	set(fieldsGroupsAtom, updatedGroups);
});

export const updateFieldMeasureAtom = atom(null, (get, set, { tempId, measure }: { tempId: string; measure: { id: number; name: string } }) => {
	const groupFields = get(fieldsGroupsAtom);
	const updatedGroups = groupFields.map(group => ({
		...group,
		items: group.items.map(field => (field.tempId === tempId ? { ...field, measure } : field)),
	}));
	set(fieldsGroupsAtom, updatedGroups);
});

export const updateFieldDataAtom = atom(null, (get, set, { tempId, field }: { tempId: string; field: { id?: number; name: string } }) => {
	const groupFields = get(fieldsGroupsAtom);
	const updatedGroups = groupFields.map(group => ({
		...group,
		items: group.items.map(fieldItem => (fieldItem.tempId === tempId ? { ...fieldItem, field } : fieldItem)),
	}));
	set(fieldsGroupsAtom, updatedGroups);
});

export const updateFieldBiFilterAtom = atom(null, (get, set, { tempId, biFilter }: { tempId: string; biFilter: boolean }) => {
	const groupFields = get(fieldsGroupsAtom);
	const updatedGroups = groupFields.map(group => ({
		...group,
		items: group.items.map(field => (field.tempId === tempId ? { ...field, biFilter } : field)),
	}));
	set(fieldsGroupsAtom, updatedGroups);
});

export const addFieldOptionAtom = atom(
	null,
	(get, set, { tempId, newOption }: { tempId: string; newOption: { sequence?: number; option: string; tempId: string; id?: number } }) => {
		const groupFields = get(fieldsGroupsAtom);
		const updatedGroups = groupFields.map(group => ({
			...group,
			items: group.items.map(field => {
				if (field.tempId === tempId) {
					const currentOptions = field.options || [];
					const nextSequence = currentOptions.length + 1;
					const optionWithSequence = { ...newOption, sequence: nextSequence };
					return { ...field, options: [...currentOptions, optionWithSequence] };
				}
				return field;
			}),
		}));
		set(fieldsGroupsAtom, updatedGroups);
	},
);

export const getFieldOptionsAtom = atom(get => {
	return (tempId: string) => {
		const groupFields = get(fieldsGroupsAtom);
		for (const group of groupFields) {
			const field = group.items.find(field => field.tempId === tempId);
			if (field) return field.options || [];
		}
		return [];
	};
});

export const removeFieldOptionAtom = atom(null, (get, set, { tempId, optionTempId }: { tempId: string; optionTempId: string }) => {
	const groupFields = get(fieldsGroupsAtom);
	const updatedGroups = groupFields.map(group => ({
		...group,
		items: group.items.map(field => {
			if (field.tempId === tempId && field.options) {
				const filteredOptions = field.options.filter(option => option.tempId !== optionTempId);
				return { ...field, options: filteredOptions };
			}
			return field;
		}),
	}));
	set(fieldsGroupsAtom, updatedGroups);
});

export const updateFieldOptionAtom = atom(
	null,
	(
		get,
		set,
		{
			tempId,
			optionTempId,
			updatedOption,
		}: { tempId: string; optionTempId: string; updatedOption: { sequence?: number; option: string; tempId: string; id?: number } },
	) => {
		const groupFields = get(fieldsGroupsAtom);
		const updatedGroups = groupFields.map(group => ({
			...group,
			items: group.items.map(field => {
				if (field.tempId === tempId && field.options) {
					const updatedOptions = field.options.map(option => (option.tempId === optionTempId ? updatedOption : option));
					return { ...field, options: updatedOptions };
				}
				return field;
			}),
		}));
		set(fieldsGroupsAtom, updatedGroups);
	},
);

export const reorderFieldOptionsAtom = atom(
	null,
	(get, set, { tempId, activeIndex, overIndex }: { tempId: string; activeIndex: number; overIndex: number }) => {
		const groupFields = get(fieldsGroupsAtom);
		const updatedGroups = groupFields.map(group => ({
			...group,
			items: group.items.map(field => {
				if (field.tempId === tempId && field.options) {
					const newOptions = [...field.options];
					const [movedItem] = newOptions.splice(activeIndex, 1);
					newOptions.splice(overIndex, 0, movedItem);

					const optionsWithUpdatedSequence = newOptions.map((option, index) => ({
						...option,
						sequence: index + 1,
					}));

					return { ...field, options: optionsWithUpdatedSequence };
				}
				return field;
			}),
		}));
		set(fieldsGroupsAtom, updatedGroups);
	},
);
