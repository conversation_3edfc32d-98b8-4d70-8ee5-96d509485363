import { flexRender, getCoreRowModel, useReactTable } from "@tanstack/react-table";
import { ComponentIcon } from "lucide-react";
import { useState } from "react";
import { EmptyStateTable } from "../../../../../../../shared/components/custom/empty/table-empty";
import { Pagination } from "../../../../../../../shared/components/custom/pagination";
import { Skeleton } from "../../../../../../../shared/components/shadcn/skeleton";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "../../../../../../../shared/components/shadcn/table";
import { usePagination } from "../../../../../../../shared/hooks/utils";
import { useFindAllInspectionCellByProductType } from "../../../../../hooks/cell-by-product-type/list/find-all.hook";
import { ISearchTerm } from "../../../../../types/tabs/search-term.type";
import { inspectionCellColumns } from "../../cell-by-components/list/columns";
import { inspectionCellByProductTypeColumns } from "./columns";

export const CellByProductTypeTable: React.FC<ISearchTerm> = ({ searchTerm }) => {
	const [rowSelection, setRowSelection] = useState({});
	const { currentPage, setCurrentPage, pageSize, setItemsPerPage } = usePagination();

	const { data, isLoading, hasError, error, pagination } = useFindAllInspectionCellByProductType({
		page: currentPage,
		limit: pageSize,
		search: searchTerm,
	});

	const table = useReactTable({
		data,
		columns: inspectionCellByProductTypeColumns,
		state: { rowSelection },
		onRowSelectionChange: setRowSelection,
		enableRowSelection: true,
		getCoreRowModel: getCoreRowModel(),
		manualPagination: true,
		pageCount: pagination?.totalPages ?? 0,
	});

	return (
		<div className="space-y-4">
			<div className="bg-background overflow-x-auto rounded-lg border">
				<Table>
					<TableHeader className="bg-muted sticky top-0 z-10">
						{table.getHeaderGroups().map(headerGroup => (
							<TableRow key={headerGroup.id}>
								{headerGroup.headers.map(header => (
									<TableHead key={header.id} colSpan={header.colSpan} className="px-4 text-center font-semibold whitespace-nowrap">
										{header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
									</TableHead>
								))}
							</TableRow>
						))}
					</TableHeader>
					<TableBody>
						{hasError ? (
							<TableRow>
								<TableCell colSpan={inspectionCellColumns.length} className="h-24 text-center text-red-500">
									Erro ao carregar os dados: {error || "Ocorreu um erro desconhecido."}
								</TableCell>
							</TableRow>
						) : isLoading ? (
							<TableRow>
								<TableCell colSpan={inspectionCellColumns.length} className="h-24 text-center">
									<Skeleton className="h-6 w-48" />
								</TableCell>
							</TableRow>
						) : table.getRowModel().rows.length ? (
							table.getRowModel().rows.map((row, idx) => (
								<TableRow
									key={row.id}
									data-state={row.getIsSelected() && "selected"}
									className={`${idx % 2 === 0 ? "bg-muted/20" : "bg-background"} hover:bg-primary/10 transition-colors`}
								>
									{row.getVisibleCells().map(cell => (
										<TableCell key={cell.id} className="h-[32px] px-4 text-center align-middle" title={String(cell.getValue() ?? "")}>
											{flexRender(cell.column.columnDef.cell, cell.getContext())}
										</TableCell>
									))}
								</TableRow>
							))
						) : (
							<TableRow>
								<TableCell colSpan={inspectionCellColumns.length} className="h-24 text-center">
									<EmptyStateTable
										searchTerm={searchTerm}
										icon={<ComponentIcon />}
										title="Nenhum resultado encontrado"
										description={searchTerm ? "Nenhum cadastro corresponde ao termo pesquisado." : "Ainda não há cadastros."}
										tip={`Você pode adicionar um novo cadastro, clicando no botão "novo" acima.`}
									/>
								</TableCell>
							</TableRow>
						)}
					</TableBody>
				</Table>
			</div>
			{pagination && (
				<Pagination
					currentPage={pagination.currentPage}
					totalPages={pagination.totalPages}
					pageSize={pagination.itemsPerPage}
					totalItems={pagination.totalItems}
					onPageChange={setCurrentPage}
					onPageSizeChange={size => {
						setItemsPerPage(size);
						setCurrentPage(1);
					}}
					showPageSizeSelector
					showSelectedInfo
				/>
			)}
		</div>
	);
};
