import { ICreateFieldForm } from "@/modules/inspection/validators/form/create-field";
import { ColumnDef } from "@tanstack/react-table";
import { requiredLabel } from "../../form";
import { DragHandle } from "../items/drag-handle";
import { getColumnWidths } from "../utils/column-widths";
import { InspectionFormActionsRow } from "./actions-row";
import { InspectionFormBiFilterRow } from "./bi-filter-row";
import { InspectionFormFieldRow } from "./field-row";
import { InspectionFormFieldTypeRow } from "./field-type-row";
import { InspectionFormMeasureRow } from "./measure-row";
import { InspectionFormNicknameRow } from "./nickname-row";
import { InspectionFormRequiredRow } from "./required-row";

const columnWidths = getColumnWidths();

export const inspectionFormColumns: ColumnDef<ICreateFieldForm>[] = [
	{
		id: "drag-handle",
		header: () => null,
		cell: ({ row }) => <DragHandle id={row.original.tempId} />,
		meta: { width: columnWidths["drag-handle"] },
	},
	{
		id: "field-name",
		header: () => requiredLabel("Campo"),
		cell: ({ row }) => <InspectionFormFieldRow row={row} />,
		meta: { width: columnWidths["field-name"] },
	},
	{
		id: "nickname",
		header: "Abreviação",
		cell: ({ row }) => <InspectionFormNicknameRow row={row} />,
		meta: { width: columnWidths["nickname"] },
	},
	{
		id: "field-type",
		header: () => requiredLabel("Tipo"),
		cell: ({ row }) => <InspectionFormFieldTypeRow row={row} />,
		meta: { width: columnWidths["field-type"] },
	},
	{
		id: "measure",
		header: () => requiredLabel("Medida"),
		cell: ({ row }) => <InspectionFormMeasureRow row={row} />,
		meta: { width: columnWidths["measure"] },
	},
	{
		id: "bi-filter",
		header: () => requiredLabel("Filtro BI"),
		cell: ({ row }) => <InspectionFormBiFilterRow row={row} />,
		meta: { width: columnWidths["bi-filter"] },
	},
	{
		id: "required",
		header: "Obrigatório",
		cell: ({ row }) => <InspectionFormRequiredRow row={row} />,
		meta: { width: columnWidths["required"] },
	},
	{
		id: "actions",
		header: "Ações",
		cell: ({ row }) => <InspectionFormActionsRow row={row} />,
		meta: { width: columnWidths["actions"] },
	},
];
