import { toast } from "@/core/toast";
import { INSPECTION_FORM_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { ICreateFormDTO } from "@/modules/inspection/types/forms/dtos/create-form.dto";

import { createPostRequest } from "@/shared/lib/requests";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { inspectionKeys } from "../../../constants/query/keys";

export const useCreateFormMutation = () => {
	const queryClient = useQueryClient();

	const createFormMutation = useMutation({
		mutationKey: inspectionKeys.forms.custom("create"),
		mutationFn: async (form: ICreateFormDTO) => {
			const { data, success } = await createPostRequest<IMessageGlobalReturn>(INSPECTION_FORM_ENDPOINTS.CREATE, form);
			if (!success) throw new Error(data.message);
			return data;
		},
		onSuccess: () => inspectionKeys.forms.invalidateAll(queryClient),
	});

	return {
		createForm: async (form: ICreateFormDTO) =>
			toast.promise(createFormMutation.mutateAsync(form), {
				loading: "Criando formulário...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			}),
	};
};
