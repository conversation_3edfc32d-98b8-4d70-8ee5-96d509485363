import { useQuery } from "@tanstack/react-query";
import { createGetRequest } from "../../../../shared/lib/requests";
import { IResponsePaginated } from "../../../../shared/types/requests/response-paginated.type";
import { ACTIVITY_ENDPOINTS } from "../../api/endpoints";
import { activityQueryKeys } from "../../constants/query";
import { IActivityDto } from "../../types/find-all.dto";

interface IFindAllActivityParams {
	page?: number;
	limit?: number;
	search?: string;
}

export const useFindAllActivity = ({ page, limit, search }: IFindAllActivityParams) => {
	const { data, isLoading, isFetched } = useQuery({
		queryKey: activityQueryKeys.list({ page, limit, search }),
		queryFn: () => createGetRequest<IResponsePaginated<IActivityDto[]>>(ACTIVITY_ENDPOINTS.FIND_ALL({ page, limit, search })),
	});

	const isNoDataFound = !data?.success && data?.status === 404;

	return {
		data: data?.success ? data.data.data : [],
		pagination: data?.success
			? {
					totalItems: data.data.totalItems,
					itemsPerPage: data.data.itemsPerPage,
					currentPage: data.data.currentPage,
					totalPages: data.data.totalPages,
				}
			: null,
		isLoading,
		hasError: isFetched && !data?.success && !isNoDataFound,
		error: !data?.success && !isNoDataFound && data?.data.message,
		isEmpty: isNoDataFound,
	};
};
