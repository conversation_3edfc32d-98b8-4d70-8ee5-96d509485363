import { validateFieldsAtom } from "@/modules/inspection/atoms/forms/fields/field-validation.atom";
import { useAtomValue } from "jotai";
import { AlertCircle, CheckCircle } from "lucide-react";

export const FieldsValidationSummary = () => {
	const fieldsValidation = useAtomValue(validateFieldsAtom);

	if (!fieldsValidation.hasFields) {
		return (
			<div className="flex items-start gap-3 rounded-lg border border-yellow-200 bg-yellow-50 p-4">
				<AlertCircle className="mt-0.5 h-4 w-4 flex-shrink-0 text-yellow-600" />
				<div className="text-yellow-800">Nenhum campo foi adicionado ao formulário. Adicione pelo menos um campo para continuar.</div>
			</div>
		);
	}

	if (fieldsValidation.hasErrors) {
		return (
			<div className="flex items-start gap-3 rounded-lg border border-red-200 bg-red-50 p-4">
				<AlertCircle className="mt-0.5 h-4 w-4 flex-shrink-0 text-red-600" />
				<div className="text-red-800">
					<div className="mb-2 font-medium">
						{fieldsValidation.validationErrors.length === 1
							? "1 campo possui informações obrigatórias não preenchidas"
							: `${fieldsValidation.validationErrors.length} campos possuem informações obrigatórias não preenchidas`}
					</div>
					<div className="text-sm">Verifique os campos destacados em vermelho na tabela abaixo.</div>
				</div>
			</div>
		);
	}

	return (
		<div className="flex items-start gap-3 rounded-lg border border-green-200 bg-green-50 p-4">
			<CheckCircle className="mt-0.5 h-4 w-4 flex-shrink-0 text-green-600" />
			<div className="text-green-800">Todos os campos estão preenchidos corretamente.</div>
		</div>
	);
};
