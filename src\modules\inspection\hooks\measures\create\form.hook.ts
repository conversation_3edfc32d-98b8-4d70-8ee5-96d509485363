import { createMeasuresSchema, ICreateMeasures } from "@/modules/inspection/validators/measures/create";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, UseFormReturn } from "react-hook-form";

interface UseCreateMeasuresReturn {
	methods: UseFormReturn<ICreateMeasures>;
}

export default function useCreateMeasures(): UseCreateMeasuresReturn {
	const methods = useForm<ICreateMeasures>({
		resolver: zodResolver(createMeasuresSchema),
		defaultValues: {
			name: "",
			abbreviation: "",
		},
		mode: "onChange",
	});

	return { methods };
}
