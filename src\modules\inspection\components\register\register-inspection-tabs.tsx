"use client";
import { Tabs, TabsContent } from "@/shared/components/shadcn/tabs";
import { useModal } from "@/shared/hooks/utils/modal.hook";
import { Circle, Component, FileText, Grid, Link, Ruler, Users } from "lucide-react";
import { useState } from "react";
import { TInspectionTabValue, useInspectionTabs } from "../../hooks/tabs/inspection-tabs.hook";
import { ITabItemRegisterInspectionTabs, RegisterInspectionHeader } from "./header/register-inspection-header";
import { ModalCreateCellComponent } from "./tabs/cell-by-components/create/modal";
import { CellComponentsTable } from "./tabs/cell-by-components/list/table";
import { CellByProductTypeTable } from "./tabs/cell-by-product-type/list/table";
import { ColaboradoresTab } from "./tabs/colaboradores-tab";
import ModalCreateFields from "./tabs/fields/create/modal";
import { CamposTab } from "./tabs/fields/list/table";
import { ModalCreateForm } from "./tabs/forms/create/modal";
import { FormulariosTab } from "./tabs/forms/list/table";
import ModalCreateMeasures from "./tabs/measures/create/modal";
import { MedidasTab } from "./tabs/measures/list/table";

export const RegisterInspectionTabs = () => {
	const { activeTab, setActiveTab, availableTabs } = useInspectionTabs();
	const [searchTerm, setSearchTerm] = useState("");
	const inspectionModals = {
		medidas: useModal(),
		campos: useModal(),
		formularios: useModal(),
		componentes: useModal(),
	};

	const TAB_CONFIG: ITabItemRegisterInspectionTabs[] = [
		{
			value: "medidas",
			label: "Medidas",
			icon: Ruler,
			renderContent: searchTerm => <MedidasTab searchTerm={searchTerm} />,
			onNew: () => inspectionModals.medidas.openModal(),
		},
		{
			value: "campos",
			label: "Campos",
			icon: Grid,
			renderContent: searchTerm => <CamposTab searchTerm={searchTerm} />,
			onNew: () => inspectionModals.campos.openModal(),
		},
		{
			value: "vinculo-colaboradores",
			label: "Vínculo de colaboradores",
			icon: Users,
			renderContent: searchTerm => <ColaboradoresTab searchTerm={searchTerm} />,
			onNew: searchTerm => console.log("Novo em Vínculo de colaboradores", searchTerm),
		},
		{
			value: "formularios",
			label: "Formulários",
			icon: FileText,
			renderContent: searchTerm => <FormulariosTab searchTerm={searchTerm} />,
			onNew: () => inspectionModals.formularios.openModal(),
		},
		{
			value: "tipo-produto",
			label: "Tipo produto",
			icon: Circle,
			renderContent: searchTerm => <CellByProductTypeTable searchTerm={searchTerm} />,
			onNew: searchTerm => console.log("Novo em Células", searchTerm),
		},
		{
			value: "componentes",
			label: "Componentes",
			icon: Component,
			renderContent: searchTerm => <CellComponentsTable searchTerm={searchTerm} />,
			onNew: () => inspectionModals.componentes.openModal(),
		},
		{
			value: "vinculos",
			label: "Vínculos",
			icon: Link,
			renderContent: searchTerm => <div>Vínculos content goes here. Termo: {searchTerm}</div>,
			onNew: searchTerm => console.log("Novo em Vínculos", searchTerm),
		},
	];

	const handleTabChange = (value: string) => {
		if (availableTabs.includes(value as TInspectionTabValue)) setActiveTab(value as TInspectionTabValue);
	};

	const activeTabItem = TAB_CONFIG.find(tab => tab.value === activeTab);

	return (
		<div className="flex h-full w-full flex-1 flex-col gap-4">
			<Tabs value={activeTab} onValueChange={handleTabChange} className="relative flex h-full w-full flex-col">
				<section id="tabs-header" className="h-auto w-full">
					<RegisterInspectionHeader
						tabItems={TAB_CONFIG}
						activeTab={activeTab}
						setActiveTab={setActiveTab}
						searchTerm={searchTerm}
						setSearchTerm={setSearchTerm}
						onNew={searchTerm => activeTabItem?.onNew(searchTerm)}
					/>
				</section>
				<section id="tabs-content" className="flex-1">
					{TAB_CONFIG.map(({ value, renderContent }) => (
						<TabsContent key={value} value={value} className="mt-0">
							{renderContent(searchTerm)}
						</TabsContent>
					))}
				</section>
			</Tabs>
			<ModalCreateForm isOpen={inspectionModals.formularios.isOpen} onClose={inspectionModals.formularios.closeModal} />
			<ModalCreateMeasures isOpen={inspectionModals.medidas.isOpen} onClose={inspectionModals.medidas.closeModal} />
			<ModalCreateFields isOpen={inspectionModals.campos.isOpen} onClose={inspectionModals.campos.closeModal} />
			<ModalCreateCellComponent isOpen={inspectionModals.componentes.isOpen} onClose={inspectionModals.componentes.closeModal} />
		</div>
	);
};
