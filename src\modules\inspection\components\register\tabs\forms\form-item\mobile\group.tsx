"use client";
import { addItem<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/modules/inspection/atoms/forms/fields/field-actions.atom";
import { removeFieldG<PERSON><PERSON><PERSON>, updateFieldGroupTitle<PERSON>tom } from "@/modules/inspection/atoms/forms/fields/group-actions.atom";
import { IFieldGroup } from "@/modules/inspection/types/forms/fields-table/fields-group.type";
import { Badge } from "@/shared/components/shadcn/badge";
import { Button } from "@/shared/components/shadcn/button";
import { Input } from "@/shared/components/shadcn/input";
import { AnimatePresence, motion } from "framer-motion";
import { useSetAtom } from "jotai";
import { ArrowDown, ArrowUp, FolderOpen, Plus, Trash2 } from "lucide-react";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { useMobileFieldsReorder } from "../../../../../../hooks/form/mobile/mobile-fields-reorder.hook";
import { IFlattenedFieldMobile } from "../../../../../../types/forms/mobile/mobile-fields-cards.type";
import { MobileFieldCard } from "./card";

interface IMobileGroupHeaderProps {
	group: IFieldGroup;
	autoFocus?: boolean;
	setActiveId: React.Dispatch<React.SetStateAction<string | null>>;
	activeId?: string | null;
	groupFields: IFlattenedFieldMobile[];
}

export const MobileGroup: React.FC<IMobileGroupHeaderProps> = React.memo(({ group, autoFocus, setActiveId, activeId, groupFields }) => {
	const { fieldsGroups, isReordering, handleMoveUp, handleMoveDown, handleMoveGroupUp, handleMoveGroupDown, canMoveGroupUp, canMoveGroupDown } =
		useMobileFieldsReorder();
	const addItemToGroup = useSetAtom(addItemToGroupAtom);
	const removeGroup = useSetAtom(removeFieldGroupAtom);
	const updateGroupTitle = useSetAtom(updateFieldGroupTitleAtom);

	const [isEditing, setIsEditing] = useState(false);
	const [groupTitle, setGroupTitle] = useState(group.groupTitle || "");
	// const [isExpanded, setIsExpanded] = useState(true);
	const inputRef = useRef<HTMLInputElement>(null);

	useEffect(() => {
		if (autoFocus && !group.groupTitle) setIsEditing(true);
	}, [autoFocus, group.groupTitle]);

	useEffect(() => {
		if (isEditing) inputRef.current?.focus();
	}, [isEditing]);

	const handleSaveTitle = () => {
		setIsEditing(false);
		if (groupTitle !== group.groupTitle) updateGroupTitle({ groupId: group.tempId, newTitle: groupTitle });
	};

	const handleKeyPress = (e: React.KeyboardEvent) => {
		if (e.key === "Enter") handleSaveTitle();
		if (e.key === "Escape") {
			setGroupTitle(group.groupTitle || "");
			setIsEditing(false);
		}
	};

	const handleCardClick = useCallback((tempId: string) => setActiveId(activeId === tempId ? null : tempId), [activeId, setActiveId]);

	const fieldsCount = group.items.length;
	const hasTitle = Boolean(group.groupTitle);
	const isEmptyGroup = group.tempId.startsWith("empty");

	const renderFields = () => (
		<AnimatePresence mode="popLayout">
			{groupFields.map((field, fieldIndex) => (
				<motion.div
					key={field.tempId}
					layout
					initial={{ opacity: 0, x: -10 }}
					animate={{ opacity: 1, x: 0 }}
					exit={{ opacity: 0, x: 10 }}
					transition={{ duration: 0.15, delay: fieldIndex * 0.02 }}
				>
					<MobileFieldCard
						field={field}
						isFirst={field.indexInGroup === 0}
						isLast={field.indexInGroup === field.totalInGroup - 1}
						isSelected={activeId === field.tempId}
						isReordering={isReordering}
						onMoveUp={isEmptyGroup ? () => handleMoveGroupUp(group) : () => handleMoveUp(field)}
						onMoveDown={isEmptyGroup ? () => handleMoveGroupDown(group) : () => handleMoveDown(field)}
						onCardClick={() => handleCardClick(field.tempId)}
						isGroupEmpty={isEmptyGroup}
						removeGroup={isEmptyGroup ? () => removeGroup(group.tempId) : undefined}
					/>
				</motion.div>
			))}
		</AnimatePresence>
	);

	if (isEmptyGroup) {
		return (
			<div className="border-border/50 from-background to-muted/20 overflow-hidden rounded-xl border bg-gradient-to-br shadow-sm transition-all duration-200 hover:shadow-md">
				<motion.div layout initial={{ opacity: 0, y: -10 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.2 }}>
					<div className="bg-background/50">
						<div className="space-y-3">{renderFields()}</div>
					</div>
				</motion.div>
			</div>
		);
	}

	return (
		<div className="border-border/50 from-background to-muted/20 overflow-hidden rounded-xl border bg-gradient-to-br shadow-sm transition-all duration-200 hover:shadow-md">
			<motion.div
				layout
				initial={{ opacity: 0, y: -10 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ duration: 0.2 }}
				className="from-primary/5 to-primary/10 border-border/50 border-b bg-gradient-to-r p-4"
			>
				<div className="flex items-center justify-between">
					<div className="flex flex-1 items-center gap-3">
						<div className="bg-primary/10 text-primary flex h-10 w-10 items-center justify-center rounded-lg">
							<FolderOpen className="h-5 w-5" />
						</div>
						<div className="min-w-0 flex-1">
							{isEditing ? (
								<Input
									ref={inputRef}
									value={groupTitle}
									onChange={e => setGroupTitle(e.target.value)}
									onBlur={handleSaveTitle}
									onKeyDown={handleKeyPress}
									placeholder="Nome do grupo"
									className="h-8 border-none bg-transparent p-0 text-base font-semibold focus:ring-0"
								/>
							) : (
								<div onClick={() => setIsEditing(true)} className="cursor-pointer rounded px-1 py-1 transition-colors hover:bg-white/50">
									<h3 className="truncate text-base font-semibold">{hasTitle ? group.groupTitle : "Sem título"}</h3>
								</div>
							)}
							<div className="mt-1 flex items-center gap-2">
								<Badge variant="secondary" className="text-xs">
									{fieldsCount} {fieldsCount === 1 ? "campo" : "campos"}
								</Badge>
								{!hasTitle && (
									<Badge variant="outline" className="text-xs">
										Clique para nomear
									</Badge>
								)}
							</div>
						</div>
					</div>
					<div className="flex items-center gap-1">
						{fieldsGroups.length > 1 && (
							<>
								<Button
									size="sm"
									variant="ghost"
									onClick={e => {
										e.preventDefault();
										handleMoveGroupUp(group);
									}}
									disabled={!canMoveGroupUp(group) || isReordering}
									className="text-muted-foreground hover:text-foreground h-8 w-8 p-0 hover:bg-white/50 disabled:opacity-30"
									title="Mover grupo para cima"
								>
									<ArrowUp className="h-4 w-4" />
								</Button>
								<Button
									size="sm"
									variant="ghost"
									onClick={e => {
										e.preventDefault();
										handleMoveGroupDown(group);
									}}
									disabled={!canMoveGroupDown(group) || isReordering}
									className="text-muted-foreground hover:text-foreground h-8 w-8 p-0 hover:bg-white/50 disabled:opacity-30"
									title="Mover grupo para baixo"
								>
									<ArrowDown className="h-4 w-4" />
								</Button>
								<div className="bg-border/50 mx-1 h-4 w-px" />
							</>
						)}

						<div className="bg-border/50 mx-1 h-4 w-px" />
						<Button
							size="sm"
							variant="ghost"
							onClick={e => {
								e.preventDefault();
								addItemToGroup(group.tempId);
							}}
							className="text-primary hover:text-primary hover:bg-primary/10 h-8 w-8 p-0"
							title="Adicionar campo ao grupo"
						>
							<Plus className="h-4 w-4" />
						</Button>

						<Button
							size="sm"
							variant="ghost"
							onClick={e => {
								e.preventDefault();
								removeGroup(group.tempId);
							}}
							className="text-muted-foreground hover:text-destructive hover:bg-destructive/10 h-8 w-8 p-0"
							title="Remover grupo"
						>
							<Trash2 className="h-4 w-4" />
						</Button>
					</div>
				</div>
			</motion.div>
			<motion.div
				initial={false}
				animate={{
					height: "auto",
					opacity: 1,
				}}
				transition={{ duration: 0.2, ease: "easeInOut" }}
				className="overflow-hidden"
			>
				<div className="bg-background/50 p-4">
					<div className="space-y-3">
						{renderFields()}
						{groupFields.length === 0 && (
							<motion.div
								initial={{ opacity: 0 }}
								animate={{ opacity: 1 }}
								className="border-muted-foreground/20 text-muted-foreground flex h-16 items-center justify-center rounded-lg border border-dashed bg-white/50"
							>
								<div className="text-center">
									<p className="text-sm">Grupo vazio</p>
									<p className="text-xs opacity-70">Adicione campos para começar</p>
								</div>
							</motion.div>
						)}
					</div>
				</div>
			</motion.div>
		</div>
	);
});

MobileGroup.displayName = "MobileGroup";
