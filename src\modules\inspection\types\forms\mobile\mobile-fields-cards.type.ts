import { ICreateFieldForm } from "@/modules/inspection/validators/form/create-field";
import { IFieldGroup } from "../fields-table/fields-group.type";

/**
 * Interface para as props do componente MobileFieldsCards
 * Equivalente mobile do TableFields com funcionalidades de reordenação
 */
export interface IMobileFieldsCardsProps {
	/** Termo de busca para filtrar campos */
	searchTerm?: string;
	/** Callback chamado quando um campo é selecionado */
	onFieldSelect?: (fieldId: string | null) => void;
	/** Callback chamado quando a ordem dos campos é alterada */
	onFieldsReorder?: (groupId: string, fromIndex: number, toIndex: number) => void;
	/** Callback chamado quando um campo é removido */
	onFieldRemove?: (fieldId: string) => void;
	/** Callback chamado quando um grupo é removido */
	onGroupRemove?: (groupId: string) => void;
	/** Se deve mostrar animações de carregamento */
	isLoading?: boolean;
	/** Se deve desabilitar interações durante operações */
	disabled?: boolean;
}

/**
 * Interface para um campo "achatado" com informações do grupo
 * Usado internamente para facilitar a manipulação dos dados
 */
export interface IFlattenedFieldMobile extends ICreateFieldForm {
	/** ID do grupo ao qual o campo pertence */
	groupId: string;
	/** Título do grupo ao qual o campo pertence */
	groupTitle?: string;
	/** Índice do campo dentro do grupo */
	indexInGroup: number;
	/** Total de campos no grupo */
	totalInGroup: number;
}

/**
 * Interface para as props do componente MobileFieldCard
 */
export interface IMobileFieldCardProps {
	/** Dados do campo */
	field: IFlattenedFieldMobile;
	/** Se é o primeiro campo do grupo */
	isFirst: boolean;
	/** Se é o último campo do grupo */
	isLast: boolean;
	/** Se o campo está selecionado */
	isSelected: boolean;
	/** Se está em processo de reordenação */
	isReordering?: boolean;
	/** Callback para mover campo para cima */
	onMoveUp: () => void;
	/** Callback para mover campo para baixo */
	onMoveDown: () => void;
	/** Callback quando o card é clicado */
	onCardClick: () => void;
}

/**
 * Interface para as props do componente MobileGroupHeader
 */
export interface IMobileGroupHeaderProps {
	/** Dados do grupo */
	group: IFieldGroup;
	/** Se deve focar automaticamente no input de título */
	autoFocus?: boolean;
	/** Callback chamado quando o título do grupo é alterado */
	onTitleChange?: (groupId: string, title: string) => void;
	/** Callback chamado quando um item é adicionado ao grupo */
	onAddItem?: (groupId: string) => void;
	/** Callback chamado quando o grupo é removido */
	onRemoveGroup?: (groupId: string) => void;
}

/**
 * Interface para configurações de animação dos cards
 */
export interface IMobileCardAnimationConfig {
	/** Duração da animação de entrada/saída em ms */
	duration?: number;
	/** Delay entre animações em ms */
	stagger?: number;
	/** Se deve usar animações de spring */
	useSpring?: boolean;
	/** Configurações específicas do spring */
	springConfig?: {
		stiffness: number;
		damping: number;
	};
}

/**
 * Interface para métricas de performance do componente
 */
export interface IMobileFieldsPerformanceMetrics {
	/** Número total de campos renderizados */
	totalFields: number;
	/** Número de grupos renderizados */
	totalGroups: number;
	/** Tempo de renderização em ms */
	renderTime?: number;
	/** Se está usando lazy loading */
	isLazyLoading: boolean;
}

/**
 * Enum para tipos de ação nos cards
 */
export enum MobileFieldActionType {
	MOVE_UP = "MOVE_UP",
	MOVE_DOWN = "MOVE_DOWN",
	DELETE = "DELETE",
	SELECT = "SELECT",
	EDIT = "EDIT",
}

/**
 * Interface para eventos de ação nos cards
 */
export interface IMobileFieldActionEvent {
	/** Tipo da ação */
	type: MobileFieldActionType;
	/** ID do campo */
	fieldId: string;
	/** ID do grupo */
	groupId: string;
	/** Dados adicionais da ação */
	payload?: Record<string, unknown>;
}
