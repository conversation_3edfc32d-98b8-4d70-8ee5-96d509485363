import { getFieldValidationErrorsAtom } from "@/modules/inspection/atoms/forms/fields/field-validation.atom";
import { InspectionFormTypeEnum } from "@/modules/inspection/constants/form/type-enum";
import { ICreateFieldForm } from "@/modules/inspection/validators/form/create-field";
import { TableCell, TableRow } from "@/shared/components/shadcn/table";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { flexRender, Row } from "@tanstack/react-table";
import { useAtomValue } from "jotai";
import { InspectionFieldOptions } from "../options/field-options";

interface SortableTableRowProps {
	row: Row<ICreateFieldForm>;
	isOverlay: boolean;
}

export const SortableTableRow = ({ row, isOverlay }: SortableTableRowProps) => {
	const getFieldErrors = useAtomValue(getFieldValidationErrorsAtom);
	const fieldErrors = getFieldErrors(row.original.tempId);
	const hasErrors = Object.keys(fieldErrors).length > 0;

	const { setNodeRef, transform, transition, isDragging } = useSortable({
		id: row.original.tempId,
		transition: {
			duration: 200,
			easing: "cubic-bezier(0.25, 1, 0.5, 1)",
		},
	});

	const isEmptyGroup = row.original.tempId.includes("empty") || false;
	const hasOptions = row.original.typeId === InspectionFormTypeEnum.OPTIONS && !isOverlay;

	const style = {
		transform: CSS.Transform.toString(transform),
		transition: isDragging ? "none" : transition,
		opacity: isDragging ? 0.5 : 1,
		zIndex: isDragging ? 999 : "auto",
	};

	return (
		<div ref={setNodeRef} style={style} data-field-id={row.original.tempId}>
			<TableRow
				key={row.id}
				className={`${isEmptyGroup ? "" : "bg-white/80 first:border-t-0 last:border-b-0"} ${hasErrors ? "border-l-4 border-l-red-500 bg-red-50/30" : ""}`}
			>
				{row.getVisibleCells().map(cell => (
					<TableCell key={cell.id} style={{ width: cell.column.columnDef.meta?.width as string }}>
						<div className="flex w-full flex-col">{flexRender(cell.column.columnDef.cell, cell.getContext())}</div>
					</TableCell>
				))}
			</TableRow>
			{/* {hasErrors && !isOverlay && (
				<TableRow>
					<TableCell colSpan={row.getVisibleCells().length} className="border-l-4 border-l-red-500 bg-red-50 px-4 py-2">
						<div className="text-sm text-red-600">
							<div className="mb-1 font-medium">Campos obrigatórios não preenchidos:</div>
							<ul className="list-inside list-disc space-y-1">
								{Object.entries(fieldErrors).map(([field, errors]) => (
									<li key={field} className="text-xs">
										<span className="font-medium">{getFieldDisplayName(field)}:</span> {errors.join(", ")}
									</li>
								))}
							</ul>
						</div>
					</TableCell>
				</TableRow>
			)} */}
			{hasOptions && (
				<TableRow>
					<TableCell colSpan={row.getVisibleCells().length} className="p-0">
						<InspectionFieldOptions row={row} />
					</TableCell>
				</TableRow>
			)}
		</div>
	);
};
